import { BehaviorSubject, combineLatest, EMPTY, filter, Observable, ReplaySubject } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { DestroyRef, Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  Alignment,
  AlignmentPeriod,
  AlignmentPeriodCalendar,
  BusinessResourceId,
  FieldFilter,
  FieldFilterOperator,
  Filter,
  GroupBy,
  GroupResourceId,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MetricResult,
  MultiLocationAnalyticsService,
  Order,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceId,
  ResourceMetricResult,
} from '@vendasta/multi-location-analytics';
import dayjs from 'dayjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { partnerId } from '../../../globals';
import { BrandContext, QueryService } from '../../metrics/query.service';
import { TimeRangeService } from '../../shared/time-range.service';
import {
  GetLocalSearchSEODataRequest,
  GetMultiListingProfileRequest,
  GetSEODataSummaryRequest,
  GetSEODataSummaryResponse,
  GetSEOSettingsRequest,
  ListingProfileApiService,
  LocalSearchData,
  Location,
  ProjectionFilter,
  SEOApiService,
  SEODataSummary,
  SEOSettingsResponse,
} from '@vendasta/listing-products';
import { DateRange } from '@vendasta/galaxy/utility/date-utils';
import { DEMO_PAID_EDITION, PROD_PAID_EDITION, SEARCH_RADIUS_LOCAL, SeoDataKeywordMetrics } from '@vendasta/local-seo';
import { TranslateService } from '@ngx-translate/core';
import { AccountsService, AppAndAddonActivationStatus } from '@vendasta/accounts/legacy';
import { BrandKeywordTableData, SEOTableData } from './brand-keyword-tracking-common';
import { formatNumber } from '@angular/common';
import { AccountGroup, AccountGroupService } from '../../account-group';
import { ProductService } from '../../core/product.service';

export interface SEODataPoint {
  date: Date;
  value: number;
}

export interface SEODataPoint {
  date: Date;
  value: number;
}

export interface MultiKeywordData {
  keyword: string;
  businessId: string;
  date: Date;
  localRank: number;
  organicRank: number;
  localStartedAt: number;
  organicStartedAt: number;
  address: string;
  business_name: string;
  metrics: SeoDataKeywordMetrics;
}

interface ProductData {
  productName: string;
  entryUrl: string;
}

export interface SidebarData {
  loading: boolean;
  napData: Location;
  seoData: SEODataSummary;
  localSearchData: LocalSearchData[];
  productData: ProductData;
  isLbFreeEdition: boolean;
  radiusSetting: number;
  metrics: SeoDataKeywordMetrics;
}

const SeoData_Stats: string[] = ['local_rank', 'organic_rank'];
const maxRank = 50;
const maxMapsRank = 20;

export interface KeywordState {
  loading: boolean;
  selectedKeyword: string;
  businessIdsByKeyword: Record<string, string[]>;
  options: KeywordOption[];
}

export interface KeywordOption {
  label: string;
  keyword: string;
}

@Injectable({ providedIn: 'root' })
export class BrandKeywordTrackingService {
  // Tracks the currently selected keyword
  private readonly keywordState$$ = new BehaviorSubject<KeywordState>({
    loading: true,
    selectedKeyword: '',
    businessIdsByKeyword: null,
    options: [],
  });
  public readonly keywordState$ = this.keywordState$$.asObservable();

  // Tracks the selected business's sidebar data
  private readonly sidebarState$$ = new BehaviorSubject<SidebarData>({
    loading: true,
    napData: null,
    seoData: null,
    localSearchData: [],
    productData: null,
    isLbFreeEdition: true,
    radiusSetting: 0,
    metrics: null,
  });
  public readonly sidebarData$: Observable<SidebarData> = this.sidebarState$$.asObservable();

  private groupId$: Observable<string>;

  private selectedRow$$ = new ReplaySubject<BrandKeywordTableData>(1);
  private selectedRow$ = this.selectedRow$$.asObservable();

  public readonly keywordMetrics$: Observable<SeoDataKeywordMetrics[]>;

  public readonly brandKeywordData$: Observable<BrandKeywordTableData[]>;

  public readonly SEOTableData$: Observable<SEOTableData[]>;

  constructor(
    private mla: MultiLocationAnalyticsService,
    private snackBar: MatSnackBar,
    private queryService: QueryService,
    private destroyRef: DestroyRef,
    private timeRangeService: TimeRangeService,
    private lpSEOService: SEOApiService,
    private translate: TranslateService,
    private listingProfileService: ListingProfileApiService,
    private accountsService: AccountsService,
    private accountGroupService: AccountGroupService,
    private readonly productService: ProductService,
  ) {
    this.groupId$ = this.queryService.brandsContext$.pipe(
      map((context: BrandContext) => context.resourceIds?.[0]?.groupId?.groupPathNodes?.[0] || ''),
      shareReplay(1),
    );

    this.fetchKeywordState();
    this.timeRangeService.setTimeRange('30days');

    const keywordQueryMetrics$ = combineLatest([
      this.keywordState$.pipe(filter((v) => !!v.businessIdsByKeyword)),
      this.timeRangeService.dateRange$,
    ]).pipe(
      switchMap(([state, [start, end]]: [KeywordState, [Date, Date]]) => {
        const range: DateRange = { start, end };
        const businessIds = state.businessIdsByKeyword[state.selectedKeyword];

        return this.mla.queryMetrics(
          generateSEODataQueryMetricsRequest(partnerId, businessIds, state.selectedKeyword, range.start, range.end),
        );
      }),
      shareReplay(1),
    );

    this.keywordMetrics$ = combineLatest([
      this.keywordState$,
      keywordQueryMetrics$,
      this.timeRangeService.dateRange$,
    ]).pipe(
      map(([state, metrics, [start, end]]: [KeywordState, QueryMetricsResponse, [Date, Date]]) => {
        return metrics.metricResults
          .map((result) =>
            this.ParseKeywordMetrics(state.selectedKeyword, result, {
              start: start,
              end: end,
            }),
          )
          .filter((metric): metric is SeoDataKeywordMetrics => metric !== null);
      }),
    );

    const seoQueryMetrics$: Observable<MultiKeywordData[]> = combineLatest([
      this.keywordState$,
      keywordQueryMetrics$,
      this.timeRangeService.dateRange$,
    ]).pipe(
      map(([state, metrics, [start, end]]: [KeywordState, QueryMetricsResponse, [Date, Date]]) => {
        return metrics.metricResults.map((result) =>
          this.processMetricsData(result, state.selectedKeyword, {
            start: start,
            end: end,
          }),
        );
      }),
    );

    this.SEOTableData$ = combineLatest([this.keywordState$, seoQueryMetrics$]).pipe(
      map(([state, metrics]: [KeywordState, MultiKeywordData[]]) => {
        const filteredData = metrics.filter((item) => item?.keyword == state.selectedKeyword);
        return this.constructSEOTableDataFromMultiKeywordData(filteredData);
      }),
    );

    this.brandKeywordData$ = combineLatest([this.keywordState$, seoQueryMetrics$]).pipe(
      map(([state, metrics]: [KeywordState, MultiKeywordData[]]) => {
        const filteredData = metrics.filter((item) => item?.keyword == state.selectedKeyword);
        return this.mapKeywordData(filteredData);
      }),
    );

    combineLatest([this.timeRangeService.dateRange$, this.selectedRow$])
      .pipe(
        switchMap(([[start, end], row]: [[Date, Date], BrandKeywordTableData]) => {
          this.sidebarState$$.next({ ...this.sidebarState$$.value, loading: true });

          const napData$ = this.listingProfileService
            .getMulti(
              new GetMultiListingProfileRequest({
                businessIds: [row.agid],
                languageCode: this.translate.currentLang || this.translate.defaultLang,
                projectionFilter: new ProjectionFilter({ richData: true, napData: true }),
              }),
            )
            .pipe(map((res) => res.listingProfiles[0]?.listingProfile?.napData));
          const seoData$ = this.lpSEOService
            .getSeoDataSummary(
              new GetSEODataSummaryRequest({
                businessId: row.agid,
                keywords: [row.keyword],
                startDate: start,
                endDate: end,
              }),
            )
            .pipe(
              map((data: GetSEODataSummaryResponse) => {
                const seodata = data.data?.[0];
                return seodata || new SEODataSummary({});
              }),
            );
          const localSearches$ = this.lpSEOService
            .getLocalSearchSeoData(
              new GetLocalSearchSEODataRequest({
                businessId: row.agid,
                keyword: row.keyword,
                startDate: start,
                endDate: end,
              }),
            )
            .pipe(map((response) => formatLocalSearchSEOData(response.localSearchData)));
          const productDetails$ = this.accountGroupService
            .getAccountGroup(row.agid)
            .pipe(
              switchMap((accountGroup: AccountGroup) =>
                this.productService
                  .loadActiveProducts(accountGroup.accountGroupId, accountGroup.marketId)
                  .pipe(map((products) => this.mapProductDetails(products, accountGroup.accountGroupId))),
              ),
            );
          const isLBFreeEdition$ = this.accountsService
            .listAppsAndAddonsActivationStatusesForBusiness(row.agid, {
              appIds: ['MS'],
              statuses: [AppAndAddonActivationStatus.ACTIVATED, AppAndAddonActivationStatus.CANCELED],
            })
            .pipe(
              map((activations) => {
                const editionId = activations?.[0]?.editionId;
                return editionId !== DEMO_PAID_EDITION && editionId !== PROD_PAID_EDITION;
              }),
            );
          const radiusSetting$ = this.lpSEOService
            .getSeoSettings(new GetSEOSettingsRequest({ businessId: row.agid }))
            .pipe(map((res: SEOSettingsResponse) => res?.localSearchRadius || SEARCH_RADIUS_LOCAL));

          const selectedBusinessMetrics$ = seoQueryMetrics$.pipe(
            map((metrics: MultiKeywordData[]) => {
              const filteredData = metrics.filter((item) => item?.businessId == row.agid);
              const latestMetric = filteredData[0]?.metrics;
              return latestMetric || {};
            }),
          );

          return combineLatest([
            napData$,
            seoData$,
            localSearches$,
            productDetails$,
            isLBFreeEdition$,
            radiusSetting$,
            selectedBusinessMetrics$,
          ]).pipe(
            map(([napData, seoData, localSearchData, productData, isLbFreeEdition, radius, metrics]) => ({
              napData: napData,
              seoData: seoData,
              localSearchData: localSearchData,
              productData: productData,
              isLbFreeEdition: isLbFreeEdition,
              radiusSetting: radius,
              metrics: metrics,
              loading: false,
            })),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((sidebarData: SidebarData) => {
        this.sidebarState$$.next(sidebarData);
      });
  }

  private fetchKeywordState(): void {
    combineLatest([this.groupId$, this.timeRangeService.dateRange$])
      .pipe(
        switchMap(([groupId, [start, end]]: [string, [Date, Date]]) => {
          const keywordSettings$ = this.mla
            .queryMetrics(generateFavoriteKeywordsQueryMetricsRequest(partnerId, groupId, end))
            .pipe(
              map((resp: QueryMetricsResponse) =>
                parseFavoriteKeywordsFromQueryMetricsResult(this.mla.unwrapMetricsResponse(resp)),
              ),
            );

          return keywordSettings$.pipe(
            switchMap((keywordsSet) => {
              if (!keywordsSet || Object.keys(keywordsSet).length === 0) {
                return [keywordsSet];
              }
              const allKeywords = Object.keys(keywordsSet);
              const seoDataQueries = allKeywords.map((keyword) => {
                const businessIdsForKeyword = keywordsSet[keyword];
                return this.mla
                  .queryMetrics(
                    generateSEODataQueryMetricsRequest(partnerId, businessIdsForKeyword, keyword, start, end),
                  )
                  .pipe(
                    map((resp: QueryMetricsResponse) => ({
                      keyword,
                      businessIdsWithData: resp.metricResults
                        .filter((result) => result?.metrics?.metrics?.length > 0)
                        .map((result) => result.resourceId.businessId.businessId),
                    })),
                  );
              });
              if (seoDataQueries.length === 0) {
                return [{}];
              }
              return combineLatest(seoDataQueries).pipe(
                map((seoDataResults) => {
                  const filteredKeywordsSet: Record<string, string[]> = {};

                  seoDataResults.forEach(({ keyword, businessIdsWithData }) => {
                    if (businessIdsWithData.length > 0) {
                      filteredKeywordsSet[keyword] = businessIdsWithData;
                    }
                  });
                  return Object.fromEntries(
                    Object.entries(filteredKeywordsSet).sort(([, a], [, b]) => b.length - a.length),
                  );
                }),
              );
            }),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe({
        next: (keywordsSet) => {
          this.keywordState$$.next({
            loading: false,
            selectedKeyword: Object.keys(keywordsSet)?.[0] || '',
            businessIdsByKeyword: keywordsSet,
            options: Object.entries(keywordsSet).map(([key, locations]) => ({
              label: this.translate.instant('BRAND_KEYWORD.LOCATIONS', { keyword: key, count: locations.length }),
              keyword: key,
            })),
          });
        },
      });
  }

  handleRowClick(row: BrandKeywordTableData): void {
    this.selectedRow$$.next(row);
  }

  private mapProductDetails(products: any[], businessId: string): ProductData {
    const product = products.find((item) => item.productId === 'MS');
    return {
      productName: product?.name || '',
      entryUrl: `/redirect/external/?accountGroupId=${businessId}&partnerId=${partnerId}&productId=MS&nextUrl=%2Fedit%2Faccount%2F${businessId}%2Fapp%2Flocal-seo%2F`,
    };
  }

  handleKeywordSelection(selectedKeyword: string): void {
    const existingState = this.keywordState$$.getValue();
    this.keywordState$$.next({
      ...existingState,
      selectedKeyword: selectedKeyword,
    });
  }

  private mapKeywordData(data: MultiKeywordData[]): BrandKeywordTableData[] {
    return data.map((item) => ({
      agid: item.businessId,
      keyword: item.keyword,
      business: item.business_name,
      address: item.address,
      mapsRank: this.createRankObject(item.localRank),
      organicRank: this.createRankObject(item.organicRank),
      mapsStartedAt: this.createRankObject(item.localStartedAt, maxMapsRank),
      organicStartedAt: this.createRankObject(item.organicStartedAt, maxRank),
    }));
  }

  private createRankObject(value: any, fallback: any = null): any {
    return {
      value: this.convertRank(value),
      rawValue: value ?? fallback,
    };
  }

  private constructSEOTableDataFromMultiKeywordData(data: MultiKeywordData[]): SEOTableData[] {
    return data.map((item) => {
      const seoTableData = new SEOTableData(
        item.keyword,
        new SEODataSummary({
          keyword: item.keyword,
          date: item.date,
          localRank: item.localRank,
          organicRank: item.organicRank,
          difficulty: null,
          searchVolume: null,
          searchRadius: null,
        }),
        null,
        null,
      );

      seoTableData.localStartedAt = this.createRankObject(item.localStartedAt);
      seoTableData.organicStartedAt = this.createRankObject(item.organicStartedAt);

      return seoTableData;
    });
  }

  convertRank(value: number): string {
    if (value === null || value === undefined || value === 0) {
      return 'NR';
    }
    if (value > maxRank) {
      return '50+';
    }
    return formatNumber(value, 'en-US', '1.0-2');
  }

  private ParseKeywordMetrics(
    keyword: string,
    data: ResourceMetricResult,
    range: DateRange,
  ): SeoDataKeywordMetrics | null {
    if (!data?.metrics?.metrics?.length) {
      return null;
    }
    return {
      dateRange: range,
      keyword: keyword,
      localRank: this.ParseRowsFromQueryMetricsResult(data.metrics.metrics, SeoData_Stats.indexOf('local_rank')),
      organicRank: this.ParseRowsFromQueryMetricsResult(data.metrics.metrics, SeoData_Stats.indexOf('organic_rank')),
    };
  }

  ParseRowsFromQueryMetricsResult(resp: MetricResult[], metricIndex: number): SEODataPoint[] {
    return resp.flatMap(
      (metric) =>
        metric?.results?.metrics?.flatMap((nestedMetric) =>
          nestedMetric?.measures ? [{ value: nestedMetric.measures[metricIndex], date: metric.dimension }] : [],
        ) || [],
    );
  }

  private processMetricsData(data: ResourceMetricResult, keyword: string, dateRange: DateRange): MultiKeywordData {
    if (!data?.metrics?.metrics?.length) {
      return null;
    }
    let metrics = data.metrics.metrics;
    const firstKeyword = metrics[0]?.results?.metrics?.[0]?.measures?.[6] || null;
    const hasMultipleKeywords = metrics.some((metric) => metric.results?.metrics?.[0]?.measures?.[6] !== firstKeyword);
    if (hasMultipleKeywords) {
      metrics = metrics.filter((metric) => metric.results?.metrics?.[0]?.measures?.[6] === firstKeyword);
    }
    if (!metrics.length) return null;
    metrics.sort((a, b) => new Date(b.dimension).getTime() - new Date(a.dimension).getTime());
    const latestMetric = metrics[0]?.results?.metrics?.[0];
    const oldestMetric = metrics[metrics.length - 1]?.results?.metrics?.[0];
    if (!latestMetric || !oldestMetric) return null;
    return {
      address: `${latestMetric.measures[2]}, ${latestMetric.measures[3]}, ${latestMetric.measures[4]}`,
      businessId: data.resourceId.businessId.businessId,
      keyword: keyword,
      business_name: latestMetric.measures[5],
      date: new Date(latestMetric.dimension),
      metrics: {
        dateRange: dateRange,
        keyword: keyword,
        localRank: ParseRowsFromQueryMetricsResult(metrics, 0),
        organicRank: ParseRowsFromQueryMetricsResult(metrics, 1),
      },
      localRank: latestMetric.measures[0],
      organicRank: latestMetric.measures[1],
      localStartedAt: oldestMetric.measures[0],
      organicStartedAt: oldestMetric.measures[1],
    };
  }

  GetLocalSEOSearchDetails(agid: string, keyword: string): Observable<LocalSearchData[]> {
    return this.timeRangeService.dateRange$.pipe(
      switchMap(([start, end]) => {
        const request = new GetLocalSearchSEODataRequest({ businessId: agid, keyword, startDate: start, endDate: end });
        return this.lpSEOService.getLocalSearchSeoData(request);
      }),
      map((response) => formatLocalSearchSEOData(response.localSearchData)),
    );
  }

  private handleError(err: HttpErrorResponse): Observable<never> {
    const message = err.error?.message || 'Unknown error';
    this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
    return EMPTY;
  }
}

function formatLocalSearchSEOData(data: LocalSearchData[]): LocalSearchData[] {
  return (
    data?.map((localSearchData) => {
      const mainIndex = localSearchData.results.findIndex((result) => result.isMainBusiness);
      if (mainIndex > 2) {
        const mainBusiness = localSearchData.results[mainIndex];
        localSearchData.results = [...localSearchData.results.slice(0, 3), mainBusiness];
      } else {
        localSearchData.results = localSearchData.results.slice(0, 3);
      }
      return localSearchData;
    }) || []
  );
}

function parseFavoriteKeywordsFromQueryMetricsResult(resp: MetricResult[]): Record<string, string[]> {
  const agentIdMap: Record<string, string[]> = {};
  resp?.forEach((metric) => {
    const agentId = metric.dimension;
    if (!agentIdMap[agentId]) agentIdMap[agentId] = [];
    metric.results.metrics.forEach((result) => {
      result.results.metrics.forEach((subResult) => {
        subResult.dimension?.forEach((keyword) => {
          if (!agentIdMap[agentId].includes(keyword)) {
            agentIdMap[agentId].push(keyword);
          }
        });
      });
    });
  });
  const keywordMap: Record<string, string[]> = {};
  Object.entries(agentIdMap).forEach(([agentId, keywords]) => {
    keywords.forEach((keyword) => {
      const lowerCaseKeyword = keyword.toLowerCase();
      const existingKey = Object.keys(keywordMap).find((k) => k.toLowerCase() === lowerCaseKeyword) || keyword;

      if (!keywordMap[existingKey]) keywordMap[existingKey] = [];
      if (!keywordMap[existingKey].includes(agentId)) keywordMap[existingKey].push(agentId);
    });
  });
  return Object.fromEntries(Object.entries(keywordMap).sort(([, a], [, b]) => b.length - a.length));
}

function ParseRowsFromQueryMetricsResult(resp: MetricResult[], metricIndex: number): SEODataPoint[] {
  return resp.flatMap(
    (metric) =>
      metric?.results?.metrics?.flatMap((nestedMetric) =>
        nestedMetric?.measures ? [{ value: nestedMetric.measures[metricIndex], date: metric.dimension }] : [],
      ) || [],
  );
}

function generateSEODataQueryMetricsRequest(
  partnerId: string,
  businessIds: string[],
  keyword: string,
  startDate: Date,
  endDate: Date,
): QueryMetricsRequest {
  return new QueryMetricsRequest({
    metricName: 'seo_data',
    partnerId,
    resourceIds: businessIds.map((id) => new ResourceId({ businessId: new BusinessResourceId({ businessId: id }) })),
    filter: new Filter({
      fieldFilter: new FieldFilter({
        dimension: 'LOWER(keyword)',
        operator: FieldFilterOperator.EQUAL,
        value: { value: keyword.toLowerCase(), valueType: PropertyType.PROPERTY_TYPE_STRING },
      }),
    }),
    dateRange: { start: startDate, end: endDate },
    measures: ['local_rank', 'organic_rank', 'address', 'city', 'state', 'company_name', 'keyword'].map(
      (measure) => new Measure({ aggregate: new MeasureAggregate({ measure, aggOp: MeasureAggregateOperator.MAX }) }),
    ),
    groupBy: new GroupBy({
      dimension: [
        { dimension: 'date' },
        { limitDimension: { dimension: 'version', order: Order.ORDER_DESC, limit: 1 } },
      ],
    }),
    alignment: Alignment.ALIGN_DELTA,
    alignmentPeriod: new AlignmentPeriod({
      calendar: getAlignmentPeriodForDayJSRange(dayjs(startDate), dayjs(endDate)),
    }),
  });
}

function generateFavoriteKeywordsQueryMetricsRequest(
  partnerId: string,
  groupId: string,
  endDate: Date,
): QueryMetricsRequest {
  const startDate = dayjs(endDate).subtract(12, 'months').toDate();
  return new QueryMetricsRequest({
    partnerId,
    metricName: 'seo_settings',
    resourceIds: [
      new ResourceId({
        groupId: new GroupResourceId({ groupPathNodes: [groupId] }),
      }),
    ],
    groupBy: new GroupBy({
      dimension: [
        { dimension: 'business_id' },
        { limitDimension: { dimension: 'updated', order: Order.ORDER_DESC, limit: 1 } },
        { dimension: 'favorite_keywords' },
      ],
    }),
    alignmentPeriod: new AlignmentPeriod({
      calendar: AlignmentPeriodCalendar.CALENDAR_MINUTE,
    }),
    alignment: Alignment.ALIGN_DELTA,
    dateRange: {
      start: startDate,
      end: addDays(endDate, 10),
    },
  });
}

function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export function getAlignmentPeriodForDayJSRange(start: dayjs.Dayjs, end: dayjs.Dayjs): AlignmentPeriodCalendar {
  const monthsDiff = end.diff(start, 'months', true);
  const daysDiff = end.diff(start, 'days');
  if (monthsDiff >= 5.5) return AlignmentPeriodCalendar.CALENDAR_MONTH;
  if (daysDiff >= 89) return AlignmentPeriodCalendar.CALENDAR_WEEK;
  return AlignmentPeriodCalendar.CALENDAR_DAY;
}
